#!/bin/bash

# Billing Monitoring Script
# This script helps detect billing issues by analyzing logs and database discrepancies

LOG_DIR="${LOG_DIR:-./logs}"
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-3306}"
DB_NAME="${DB_NAME:-oneapi}"
DB_USER="${DB_USER:-root}"

echo "=== One-API Billing Health Monitor ==="
echo "Timestamp: $(date)"
echo

# 1. Check for billing timeout errors
echo "1. Checking for billing timeout errors..."
if [ -d "$LOG_DIR" ]; then
    timeout_count=$(grep -r "CRITICAL BILLING TIMEOUT" "$LOG_DIR" 2>/dev/null | wc -l)
    if [ "$timeout_count" -gt 0 ]; then
        echo "⚠️  CRITICAL: Found $timeout_count billing timeout errors!"
        echo "Recent timeout errors:"
        grep -r "CRITICAL BILLING TIMEOUT" "$LOG_DIR" 2>/dev/null | tail -5
    else
        echo "✅ No billing timeout errors found"
    fi
else
    echo "⚠️  Log directory not found: $LOG_DIR"
fi
echo

# 2. Check for database connection issues
echo "2. Checking for database connection issues..."
if [ -d "$LOG_DIR" ]; then
    db_errors=$(grep -r "CRITICAL DB CONNECTION BOTTLENECK\|HIGH DB CONNECTION USAGE" "$LOG_DIR" 2>/dev/null | wc -l)
    if [ "$db_errors" -gt 0 ]; then
        echo "⚠️  WARNING: Found $db_errors database connection issues!"
        echo "Recent connection issues:"
        grep -r "CRITICAL DB CONNECTION BOTTLENECK\|HIGH DB CONNECTION USAGE" "$LOG_DIR" 2>/dev/null | tail -3
    else
        echo "✅ No database connection issues found"
    fi
else
    echo "⚠️  Cannot check database connection issues - log directory not found"
fi
echo

# 3. Check for failed log recordings
echo "3. Checking for failed log recordings..."
if [ -d "$LOG_DIR" ]; then
    log_failures=$(grep -r "failed to record log\|insert user request cost failed" "$LOG_DIR" 2>/dev/null | wc -l)
    if [ "$log_failures" -gt 0 ]; then
        echo "⚠️  WARNING: Found $log_failures log recording failures!"
        echo "Recent log failures:"
        grep -r "failed to record log\|insert user request cost failed" "$LOG_DIR" 2>/dev/null | tail -3
    else
        echo "✅ No log recording failures found"
    fi
else
    echo "⚠️  Cannot check log recording failures - log directory not found"
fi
echo

# 4. Check for quota calculation issues
echo "4. Checking for quota calculation issues..."
if [ -d "$LOG_DIR" ]; then
    quota_issues=$(grep -r "totalQuota consumed is.*something is wrong" "$LOG_DIR" 2>/dev/null | wc -l)
    if [ "$quota_issues" -gt 0 ]; then
        echo "⚠️  WARNING: Found $quota_issues quota calculation issues!"
        echo "Recent quota issues:"
        grep -r "totalQuota consumed is.*something is wrong" "$LOG_DIR" 2>/dev/null | tail -3
    else
        echo "✅ No quota calculation issues found"
    fi
else
    echo "⚠️  Cannot check quota calculation issues - log directory not found"
fi
echo

# 5. Database consistency check (if mysql client is available)
echo "5. Database consistency check..."
if command -v mysql >/dev/null 2>&1; then
    echo "Checking for quota vs log discrepancies..."
    
    # This query finds users with quota deductions but missing logs in the last 24 hours
    mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
    SELECT 
        'Potential Missing Billing Records' as issue_type,
        COUNT(*) as count
    FROM (
        SELECT u.id, u.used_quota, COALESCE(SUM(l.quota), 0) as logged_quota
        FROM users u
        LEFT JOIN logs l ON u.id = l.user_id 
            AND l.type = 2 
            AND l.created_at > UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 24 HOUR))
        WHERE u.used_quota > 0
        GROUP BY u.id, u.used_quota
        HAVING u.used_quota > logged_quota * 1.1
    ) as discrepancies;
    " 2>/dev/null || echo "⚠️  Database connection failed or credentials not provided"
else
    echo "⚠️  MySQL client not available - skipping database consistency check"
fi
echo

# 6. Summary and recommendations
echo "=== SUMMARY AND RECOMMENDATIONS ==="
echo
echo "If you found any issues above:"
echo "1. CRITICAL BILLING TIMEOUT: Increase SQL_MAX_OPEN_CONNS and SQL_MAX_IDLE_CONNS"
echo "2. DB CONNECTION ISSUES: Monitor database performance and consider scaling"
echo "3. LOG RECORDING FAILURES: Check disk space and database permissions"
echo "4. QUOTA CALCULATION ISSUES: Review model pricing configuration"
echo
echo "Environment variables you can set:"
echo "- SQL_MAX_OPEN_CONNS=2000 (default: 1000)"
echo "- SQL_MAX_IDLE_CONNS=200 (default: 100)"
echo "- SQL_MAX_LIFETIME=300 (default: 60)"
echo
echo "Monitor completed at: $(date)"
